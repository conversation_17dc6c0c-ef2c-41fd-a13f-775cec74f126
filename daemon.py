# Pillar PiKernel Invoice Processor Background Processor
# This script is designed to run as a background process to poll an S3 bucket for new invoices and process them.
# If it has permission, it will automatically shut down the EC2 instance when it has been idle for a hardcoded period of time. This is to prevent the instance from staying on indefinitely.

import src.controllers.invoice_controller_graph as invoice_controller
import shutil
import os
import subprocess

def can_run_command(command):
    # Check if the command exists in PATH
    path = shutil.which(command)
    if not path:
        return False  # Not found

    # Check if it's executable
    return os.access(path, os.X_OK)

if __name__ == "__main__":
    can_shutdown = can_run_command("shutdown")
    if can_shutdown:
        print("[daemon] Starting background processing daemon. Linux will shutdown when complete.")
    else:
        print("[daemon] Starting background processing daemon. Shutdown permissions are not enabled, so Linux will not shutdown when complete.")
    controller = invoice_controller.InvoiceController(".", ".")
    exit_code = controller.process_invoices_in_s3_bucket("openai", "gpt-4")
    if exit_code == 0:
        if can_shutdown:
            subprocess.run(["shutdown", "-h", "now"])
            print("[daemon] Shutting down Linux...")
            exit(0)
        else:
            print("[daemon] Not shutting down Linux (no permission). Exiting...")
            exit(0)
    else:
        raise Exception("Error processing invoices. Exiting...")
